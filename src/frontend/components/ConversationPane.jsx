import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import LoadingSpinner from './LoadingSpinner.jsx';
import { SHORTCUTS, matchesShortcut } from '../config/keyboardShortcuts.js';

// Import messageNavigator utilities
const {
  buildRealIndex,
  scrollTo,
  clearHighlight,
  buildLazyLoadIndex,
  DEFAULT_PAGE_SIZE,
  HIGHLIGHT_CLASS
} = require('../utils/messageNavigator.js');

export default function ConversationPane({
  messages = [],
  onSend = () => {},
  onScrollToMessage = null,
  enableLazyLoading = false,
  lazyLoadPageSize = DEFAULT_PAGE_SIZE,
  autoScrollToBottom = true
}) {
  const [text, setText] = useState('');
  const [visibleMessages, setVisibleMessages] = useState(messages);
  const [currentPage, setCurrentPage] = useState(0);
  const [highlightedMessageId, setHighlightedMessageId] = useState(null);
  const [isSending, setIsSending] = useState(false);

  const containerRef = useRef(null);
  const messageIndexRef = useRef(new Map());
  const lazyLoadIndexRef = useRef(null);

  // Build lazy loading index when messages change
  const lazyLoadIndex = useMemo(() => {
    if (!enableLazyLoading || messages.length <= lazyLoadPageSize) {
      return null;
    }
    return buildLazyLoadIndex(messages, lazyLoadPageSize);
  }, [messages, enableLazyLoading, lazyLoadPageSize]);

  // Update lazy load index ref
  useEffect(() => {
    lazyLoadIndexRef.current = lazyLoadIndex;
  }, [lazyLoadIndex]);

  // Initialize visible messages for lazy loading
  useEffect(() => {
    if (enableLazyLoading && lazyLoadIndex) {
      // Load first few pages initially
      const initialPages = Math.min(2, lazyLoadIndex.totalPages);
      const initialMessages = [];
      for (let i = 0; i < initialPages; i++) {
        initialMessages.push(...lazyLoadIndex.pages[i].messages);
      }
      setVisibleMessages(initialMessages);
      setCurrentPage(initialPages - 1);
    } else {
      setVisibleMessages(messages);
    }
  }, [messages, enableLazyLoading, lazyLoadIndex]);

  // Build message index when visible messages change
  useEffect(() => {
    if (containerRef.current && visibleMessages.length > 0) {
      // Use a timeout to ensure DOM is updated
      setTimeout(() => {
        messageIndexRef.current = buildRealIndex(containerRef.current, visibleMessages);
      }, 100);
    }
  }, [visibleMessages]);

  // Auto-scroll to bottom when messages change (if enabled)
  useEffect(() => {
    if (autoScrollToBottom && containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [messages, autoScrollToBottom]);

  // Scroll to message function
  const scrollToMessage = useCallback(async (messageId, options = {}) => {
    if (!containerRef.current || !messageIndexRef.current) return null;

    // If lazy loading is enabled, ensure the message is loaded
    if (enableLazyLoading && lazyLoadIndexRef.current) {
      const messageIndex = messages.findIndex(m => (m.message_id || m.id) === messageId);
      if (messageIndex !== -1) {
        const targetPage = Math.floor(messageIndex / lazyLoadPageSize);
        if (targetPage > currentPage) {
          // Load more pages up to the target
          const newMessages = [];
          for (let i = 0; i <= targetPage; i++) {
            if (i < lazyLoadIndexRef.current.pages.length) {
              newMessages.push(...lazyLoadIndexRef.current.pages[i].messages);
            }
          }
          setVisibleMessages(newMessages);
          setCurrentPage(targetPage);

          // Wait for DOM update before scrolling
          setTimeout(() => {
            messageIndexRef.current = buildRealIndex(containerRef.current, newMessages);
            scrollTo(containerRef.current, messageId, messageIndexRef.current, options);
          }, 100);
          return;
        }
      }
    }

    return scrollTo(containerRef.current, messageId, messageIndexRef.current, options);
  }, [enableLazyLoading, currentPage, lazyLoadPageSize, messages]);

  // Expose scroll function to parent component
  useEffect(() => {
    if (onScrollToMessage) {
      if (typeof onScrollToMessage === 'object' && onScrollToMessage.current !== undefined) {
        // Ref-based callback (new enhanced mode)
        onScrollToMessage.current = scrollToMessage;
      }
      // For backward compatibility, we also support direct function calls in the scroll helper
    }
  }, [onScrollToMessage, scrollToMessage]);

  // Handle lazy loading on scroll
  const handleScroll = useCallback(() => {
    if (!enableLazyLoading || !lazyLoadIndexRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // Load more when near bottom
    if (scrollTop + clientHeight >= scrollHeight - 200) {
      const nextPage = currentPage + 1;
      if (nextPage < lazyLoadIndexRef.current.totalPages &&
          lazyLoadIndexRef.current.pages[nextPage] &&
          lazyLoadIndexRef.current.pages[nextPage].messages) {
        const newMessages = [...visibleMessages, ...lazyLoadIndexRef.current.pages[nextPage].messages];
        setVisibleMessages(newMessages);
        setCurrentPage(nextPage);
      }
    }
  }, [enableLazyLoading, currentPage, visibleMessages]);

  async function handleSubmit(e) {
    e.preventDefault();
    const trimmed = text.trim();
    if (!trimmed || isSending) return;
  
    try {
      setIsSending(true);
      await onSend(trimmed);
      setText('');
    } catch (error) {
      console.error('发送消息失败:', error);
    } finally {
      setIsSending(false);
    }
  }
  
  /**
   * Handle keydown in the input:
   * - Ctrl+Enter or Meta+Enter => send message (when not Shift)
   * - Shift+Enter => insert newline at cursor
   *
   * Notes:
   * - We only trigger send when the input is focused (this handler is attached to the input)
   * - On macOS users use the Command key which maps to event.metaKey
   */
  function handleKeyDown(e) {
    // Use configurable SHORTCUTS matcher where possible
    // Send shortcut: Ctrl/Cmd + Enter
    if (matchesShortcut(e, SHORTCUTS.send)) {
      e.preventDefault();
      const trimmed = text.trim();
      if (!trimmed || isSending) return;
      (async () => {
        try {
          setIsSending(true);
          await onSend(trimmed);
          setText('');
        } catch (error) {
          console.error('发送消息失败:', error);
        } finally {
          setIsSending(false);
        }
      })();
      return;
    }
  
    // Newline shortcut: Shift + Enter
    if (matchesShortcut(e, SHORTCUTS.newline)) {
      try {
        const input = e.target;
        if (input && typeof input.selectionStart === 'number') {
          const start = input.selectionStart;
          const end = input.selectionEnd;
          const before = text.slice(0, start);
          const after = text.slice(end);
          const newText = before + '\n' + after;
          setText(newText);
  
          // Move cursor to after the inserted newline on next tick
          setTimeout(() => {
            if (input.setSelectionRange) {
              const pos = start + 1;
              input.setSelectionRange(pos, pos);
            }
          }, 0);
        } else {
          setText(prev => prev + '\n');
        }
      } catch (err) {
        console.error('插入换行失败', err);
      }
      e.preventDefault();
      return;
    }
  }

  // Clear highlight when clicking elsewhere
  const handleContainerClick = useCallback((e) => {
    if (e.target === containerRef.current) {
      clearHighlight(containerRef.current);
      setHighlightedMessageId(null);
    }
  }, []);

  // Esc key handler: when focus is not inside an input/textarea/select, clear highlights.
  useEffect(() => {
    function onKeyDown(e) {
      if (e.key !== 'Escape') return;

      const targetTag = e.target && e.target.tagName ? e.target.tagName.toUpperCase() : '';
      const editableTags = ['INPUT', 'TEXTAREA', 'SELECT'];

      // If focus is inside an editable control, do nothing
      if (editableTags.includes(targetTag)) return;

      // If container not mounted or not visible, ignore
      if (!containerRef.current || containerRef.current.offsetParent === null) return;

      try {
        clearHighlight(containerRef.current);
        setHighlightedMessageId(null);
      } catch (err) {
        // noop
      }
    }

    window.addEventListener('keydown', onKeyDown, true);
    return () => window.removeEventListener('keydown', onKeyDown, true);
  }, []);
  // Keyboard navigation: ArrowUp / ArrowDown to move focus between messages.
  // We attach a keydown handler on the messages container and make it focusable (tabIndex=0)
  // so users can focus the list and use arrow keys to navigate.
  const handleContainerKeyDown = useCallback((e) => {
    if (e.key !== 'ArrowUp' && e.key !== 'ArrowDown') return;

    // If focus is inside an input/textarea/select, ignore
    const targetTag = e.target && e.target.tagName ? e.target.tagName.toUpperCase() : '';
    const editableTags = ['INPUT', 'TEXTAREA', 'SELECT'];
    if (editableTags.includes(targetTag)) return;

    // Determine current highlighted index in visibleMessages
    const ids = visibleMessages.map(m => m.message_id || m.id);
    let currentIndex = ids.indexOf(highlightedMessageId);
    if (currentIndex === -1) {
      // If none highlighted, try to use DOM activeElement inside container
      const active = document.activeElement;
      if (active && active.dataset && active.dataset.messageId) {
        currentIndex = ids.indexOf(active.dataset.messageId);
      }
    }

    const dir = e.key === 'ArrowDown' ? 1 : -1;
    let targetIndex = currentIndex + dir;

    // clamp
    if (targetIndex < 0) targetIndex = 0;
    if (targetIndex >= ids.length) targetIndex = ids.length - 1;
    const targetId = ids[targetIndex];

    if (targetId) {
      // If target not currently in DOM (lazy loading), use scrollToMessage which will load pages
      if (!messageIndexRef.current.has(targetId) && enableLazyLoading) {
        // trigger scrollToMessage which will also load pages as needed
        scrollToMessage(targetId, { highlight: true, behavior: 'smooth' });
      } else {
        // find element and focus it
        const el = containerRef.current && containerRef.current.querySelector(`[data-message-id="${targetId}"]`);
        if (el) {
          el.focus();
          // scroll into view and highlight via existing utility
          try {
            scrollTo(containerRef.current, targetId, messageIndexRef.current, { highlight: true, behavior: 'smooth' });
          } catch (err) {
            // fallback
            el.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
          setHighlightedMessageId(targetId);
        }
      }

      e.preventDefault();
      e.stopPropagation();
    }
  }, [visibleMessages, highlightedMessageId, enableLazyLoading, scrollToMessage]);

  return (
    <div className="conversation-pane" style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <div
        ref={containerRef}
        className="messages"
        tabIndex={0}
        onKeyDown={handleContainerKeyDown}
        style={{ flex: 1, overflow: 'auto', padding: 12 }}
        onScroll={handleScroll}
        onClick={handleContainerClick}
      >
        {visibleMessages.length === 0 ? (
          <div style={{ color: '#666' }}>暂无消息</div>
        ) : (
          visibleMessages.map(m => {
            const messageId = m.message_id || m.id;
            const isHighlighted = highlightedMessageId === messageId;
            return (
              <div
                key={messageId || Math.random()}
                data-message-id={messageId}
                data-testid={`message-${messageId}`}
                tabIndex={0}
                role="article"
                aria-current={isHighlighted ? 'true' : 'false'}
                className={`message ${isHighlighted ? HIGHLIGHT_CLASS : ''}`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    try {
                      scrollTo(containerRef.current, messageId, messageIndexRef.current, { highlight: true, behavior: 'smooth' });
                    } catch (err) {
                      const el = containerRef.current && containerRef.current.querySelector(`[data-message-id="${messageId}"]`);
                      if (el) el.focus();
                    }
                    setHighlightedMessageId(messageId);
                  }
                }}
                style={{
                  marginBottom: 8,
                  transition: 'background-color 0.3s ease',
                  padding: 4,
                  borderRadius: 4
                }}
              >
                <div style={{ fontSize: 12, color: '#888' }}>{m.sender || m.role || 'user'}</div>
                <div style={{
                  background: '#f5f5f5',
                  padding: 8,
                  borderRadius: 6,
                  whiteSpace: 'pre-wrap'
                }}>
                  {m.content}
                </div>
              </div>
            );
          })
        )}

        {/* Loading indicator for lazy loading */}
        {enableLazyLoading && lazyLoadIndexRef.current && currentPage < lazyLoadIndexRef.current.totalPages - 1 && (
          <div style={{ textAlign: 'center', padding: 16, color: '#666' }}>
            <div>加载更多消息...</div>
            <div style={{ fontSize: 12, marginTop: 4 }}>
              已显示 {visibleMessages.length} / {messages.length} 条消息
            </div>
          </div>
        )}

        {/* Test helper button (hidden in production) */}
        {onScrollToMessage && (
          <button
            data-testid="scroll-helper"
            style={{ display: 'none' }}
            onClick={() => {
              const firstMessage = visibleMessages[0];
              if (firstMessage) {
                const messageId = firstMessage.message_id || firstMessage.id;

                // Support both function and ref-based callbacks for backward compatibility
                if (typeof onScrollToMessage === 'function') {
                  onScrollToMessage(messageId);
                } else if (typeof onScrollToMessage === 'object' && onScrollToMessage.current) {
                  onScrollToMessage.current(messageId);
                } else {
                  scrollToMessage(messageId);
                }
              }
            }}
          >
            Test Scroll Helper
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} style={{ borderTop: '1px solid #eee', padding: 8, display: 'flex' }}>
        <input
          value={text}
          onChange={e => setText(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="输入消息并回车发送（Ctrl/Cmd+Enter 发送，Shift+Enter 换行）"
          disabled={isSending}
          style={{
            flex: 1,
            padding: 8,
            borderRadius: 4,
            border: '1px solid #ddd',
            opacity: isSending ? 0.6 : 1
          }}
        />
        <button
          type="submit"
          disabled={isSending || !text.trim()}
          style={{
            marginLeft: 8,
            padding: '8px 16px',
            backgroundColor: isSending || !text.trim() ? '#ccc' : '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: 4,
            cursor: isSending || !text.trim() ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: 6
          }}
        >
          {isSending && <LoadingSpinner size={14} color="white" />}
          {isSending ? '发送中...' : '发送'}
        </button>
      </form>


    </div>
  );
}