/**
 * Real rendering tests for ConversationPane and SummaryPane using @testing-library/react (CommonJS)
 * Adjusted to use regex/text containment for matching JSON-encoded summary content.
 */
 
const React = require('react');
const { render, screen, fireEvent } = require('@testing-library/react');
require('@testing-library/jest-dom');
 
const ConversationPane = require('../../src/frontend/components/ConversationPane.jsx').default;
const SummaryPane = require('../../src/frontend/components/SummaryPane.jsx').default;

test('ConversationPane renders messages and calls onScrollToMessage via helper', () => {
  const messages = [
    { message_id: 101, turn_id: 1, role: 'user', content: 'Hello' },
    { message_id: 102, turn_id: 1, role: 'assistant', content: 'Hi' }
  ];
  const onScroll = jest.fn();
  render(React.createElement(ConversationPane, { messages, onScrollToMessage: onScroll }));

  // messages rendered
  expect(screen.getByTestId('message-101')).toBeInTheDocument();
  expect(screen.getByTestId('message-102')).toBeInTheDocument();

  // trigger the hidden helper button to simulate scroll
  const helper = screen.getByTestId('scroll-helper');
  fireEvent.click(helper);
  expect(onScroll).toHaveBeenCalledWith(101);
});

test('SummaryPane renders summaries and onClickSummary is invoked', () => {
  const summaries = [
    { id: 's1', title: 'T1', snippet: 'snippet1', message_id: 201 },
    { id: 's2', title: 'T2', snippet: 'snippet2', message_id: 202 }
  ];
  const onClick = jest.fn();
  render(React.createElement(SummaryPane, { summaries, onSelect: onClick }));

  const item = screen.getByTestId('summary-s1');
  expect(item).toBeInTheDocument();
  fireEvent.click(item);
  expect(onClick).toHaveBeenCalledWith(summaries[0]);
});