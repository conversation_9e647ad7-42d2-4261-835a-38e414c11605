/**
 * App.test.js
 *
 * Basic integration tests for App layout and key UI pieces.
 * Uses @testing-library/react + Jest.
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import App from '../../src/frontend/components/App.jsx';

test('renders summary pane header', () => {
  render(<App />);
  // SummaryPane contains the header "📋 对话摘要"
  expect(screen.getByText(/对话摘要/)).toBeInTheDocument();
});

test('renders conversation pane area', () => {
  render(<App />);
  // ConversationPane shows "暂无消息" when no messages
  expect(screen.getByText(/暂无消息/)).toBeInTheDocument();
});