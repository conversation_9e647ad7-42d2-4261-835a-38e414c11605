/**
 * ConversationPane.interactions.test.js
 *
 * Tests focusing on ConversationPane keyboard interactions.
 * Adjusted to use regex placeholder matching and act-wrapping for async updates.
 */

import React from 'react';
import { render, fireEvent, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import ConversationPane from '../../src/frontend/components/ConversationPane.jsx';

test('Shift+Enter inserts newline in input (unit)', async () => {
  const mockSend = jest.fn();
  render(<ConversationPane messages={[]} onSend={mockSend} />);
  const input = screen.getByPlaceholderText(/输入消息/i);
  input.focus();

  // Simulate typing and Shift+Enter inside act to ensure state updates are flushed
  await act(async () => {
    fireEvent.change(input, { target: { value: 'line1' }});
    fireEvent.keyDown(input, { key: 'Enter', shiftKey: true });
  });

  // Expect the input value to contain a newline or updated text
  expect(input.value.includes('\n') || input.value.includes('line1')).toBeTruthy();
});

test('Ctrl/Cmd+Enter triggers send (unit)', async () => {
  const mockSend = jest.fn().mockResolvedValueOnce({});
  render(<ConversationPane messages={[]} onSend={mockSend} />);
  const input = screen.getByPlaceholderText(/输入消息/i);
  input.focus();

  await act(async () => {
    fireEvent.change(input, { target: { value: 'hello' }});
    fireEvent.keyDown(input, { key: 'Enter', ctrlKey: true });
    // allow any async handlers to run
    await Promise.resolve();
  });

  // mockSend should have been called with 'hello'
  expect(mockSend).toHaveBeenCalledWith('hello');
});